import { DocumentStore, IDocumentStore } from "ravendb";

export interface SampleDocument {
  id?: string;
  name: string;
  description: string;
  createdAt: string;
}

export class RavenDBService {
  private store: IDocumentStore | null = null;

  constructor() {
    this.initializeStore();
  }

  private initializeStore() {
    try {
      // Use configuration from config file
      this.store = new DocumentStore(
        ["http://localhost:8080"], // RAVENDB_CONFIG.serverUrls,
        "SampleDatabase" // RAVENDB_CONFIG.databaseName
      );

      this.store.initialize();
    } catch (error) {
      console.error("Failed to initialize RavenDB store:", error);
      this.store = null;
    }
  }

  async testConnection(): Promise<boolean> {
    if (!this.store) {
      return false;
    }

    try {
      const session = this.store.openSession();
      try {
        // Try to load a document to test the connection
        await session.load("test");
        return true;
      } finally {
        session.dispose();
      }
    } catch (error) {
      console.error("Connection test failed:", error);
      return false;
    }
  }

  async getDocuments(): Promise<SampleDocument[]> {
    if (!this.store) {
      throw new Error("RavenDB store not initialized");
    }

    const session = this.store.openSession();
    try {
      // Query all documents of type SampleDocument
      const documents = await session
        .query<SampleDocument>({ collection: "SampleDocuments" })
        .all();

      return documents;
    } finally {
      session.dispose();
    }
  }

  async createSampleData(): Promise<void> {
    if (!this.store) {
      throw new Error("RavenDB store not initialized");
    }

    const session = this.store.openSession();
    try {
      const sampleDocuments: SampleDocument[] = [
        {
          name: "Sample Document 1",
          description: "This is the first sample document created for testing.",
          createdAt: new Date().toISOString(),
        },
        {
          name: "Sample Document 2",
          description:
            "This is the second sample document with different content.",
          createdAt: new Date().toISOString(),
        },
        {
          name: "Sample Document 3",
          description:
            "This is the third sample document to demonstrate data fetching.",
          createdAt: new Date().toISOString(),
        },
      ];

      // Store the documents
      for (const doc of sampleDocuments) {
        await session.store(doc, undefined, {
          collection: "SampleDocuments",
        });
      }

      await session.saveChanges();
    } finally {
      session.dispose();
    }
  }

  async createDocument(
    document: Omit<SampleDocument, "id" | "createdAt">
  ): Promise<string> {
    if (!this.store) {
      throw new Error("RavenDB store not initialized");
    }

    const session = this.store.openSession();
    try {
      const newDocument: SampleDocument = {
        ...document,
        createdAt: new Date().toISOString(),
      };

      await session.store(newDocument, undefined, {
        collection: "SampleDocuments",
      });

      await session.saveChanges();

      return newDocument.id || "";
    } finally {
      session.dispose();
    }
  }

  async deleteDocument(id: string): Promise<void> {
    if (!this.store) {
      throw new Error("RavenDB store not initialized");
    }

    const session = this.store.openSession();
    try {
      await session.delete(id);
      await session.saveChanges();
    } finally {
      session.dispose();
    }
  }

  dispose() {
    if (this.store) {
      this.store.dispose();
      this.store = null;
    }
  }
}
