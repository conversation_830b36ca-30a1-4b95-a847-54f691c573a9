import { useEffect, useState } from "react";
import type { SampleDocument } from "./services/ravendb";
import { RavenDBService } from "./services/ravendb";

function App() {
  const [documents, setDocuments] = useState<SampleDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] =
    useState<string>("Connecting...");

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const ravenService = new RavenDBService();

        // Test connection
        const isConnected = await ravenService.testConnection();
        if (isConnected) {
          setConnectionStatus("Connected");

          // Fetch documents
          const docs = await ravenService.getDocuments();
          setDocuments(docs);
        } else {
          setConnectionStatus("Failed to connect");
          setError("Could not connect to RavenDB");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
        setConnectionStatus("Connection error");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleCreateSampleData = async () => {
    try {
      const ravenService = new RavenDBService();
      await ravenService.createSampleData();

      // Refresh the data
      const docs = await ravenService.getDocuments();
      setDocuments(docs);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to create sample data"
      );
    }
  };

  return (
    <div style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}>
      <h1>RavenDB Sample Application</h1>

      <div style={{ marginBottom: "20px" }}>
        <h2>
          Connection Status:{" "}
          <span
            style={{
              color: connectionStatus === "Connected" ? "green" : "red",
            }}
          >
            {connectionStatus}
          </span>
        </h2>
      </div>

      {error && (
        <div
          style={{
            color: "red",
            marginBottom: "20px",
            padding: "10px",
            border: "1px solid red",
            borderRadius: "4px",
          }}
        >
          Error: {error}
        </div>
      )}

      <div style={{ marginBottom: "20px" }}>
        <button
          onClick={handleCreateSampleData}
          style={{
            padding: "10px 20px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Create Sample Data
        </button>
      </div>

      <h2>Documents ({documents.length})</h2>

      {loading ? (
        <p>Loading...</p>
      ) : (
        <div>
          {documents.length === 0 ? (
            <p>
              No documents found. Click "Create Sample Data" to add some sample
              documents.
            </p>
          ) : (
            <div style={{ display: "grid", gap: "10px" }}>
              {documents.map((doc) => (
                <div
                  key={doc.id}
                  style={{
                    border: "1px solid #ccc",
                    padding: "15px",
                    borderRadius: "4px",
                    backgroundColor: "#f9f9f9",
                  }}
                >
                  <h3>{doc.name}</h3>
                  <p>
                    <strong>ID:</strong> {doc.id}
                  </p>
                  <p>
                    <strong>Description:</strong> {doc.description}
                  </p>
                  <p>
                    <strong>Created:</strong>{" "}
                    {new Date(doc.createdAt).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default App;
